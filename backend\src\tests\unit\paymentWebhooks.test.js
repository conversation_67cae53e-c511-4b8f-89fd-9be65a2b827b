const request = require('supertest');
const express = require('express');
const paymentController = require('../../controllers/paymentController');
const Order = require('../../models/Order');
const eventBus = require('../../events/eventBus');

// Mock dependencies
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    webhooks: {
      constructEvent: jest.fn()
    }
  }));
});

jest.mock('../../models/Order', () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  find: jest.fn(),
  countDocuments: jest.fn(),
  aggregate: jest.fn()
}));

jest.mock('../../events/eventBus', () => ({
  publish: jest.fn(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn()
}));

jest.mock('../../config/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

const Stripe = require('stripe');
const stripe = new Stripe('test_key');

// Setup Express app for testing
const app = express();
app.use('/webhook', express.raw({ type: 'application/json' }), paymentController.handleWebhook);

describe('Payment Webhooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test123';

    // Reset all mocks to their default implementations
    Order.findOne.mockReset();
    eventBus.publish.mockReset();
    stripe.webhooks.constructEvent.mockReset();
  });

  describe('POST /webhook', () => {
    it('should handle payment_intent.succeeded event', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test123',
            amount: 2000,
            metadata: {
              userId: 'test-user-123',
              orderId: 'order-123'
            }
          }
        }
      };

      const mockOrder = {
        id: 'order-123',
        orderNumber: 'ORD-001',
        payment: {
          status: 'pending'
        },
        save: jest.fn().mockResolvedValue()
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      Order.findOne.mockResolvedValue(mockOrder);
      eventBus.publish.mockResolvedValue();

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });

      // Verify order was updated
      expect(mockOrder.payment.status).toBe('completed');
      expect(mockOrder.payment.transactionId).toBe('pi_test123');
      expect(mockOrder.payment.processedAt).toBeInstanceOf(Date);
      expect(mockOrder.save).toHaveBeenCalled();

      // Verify event was published
      expect(eventBus.publish).toHaveBeenCalledWith(
        'order.payment.processed',
        expect.objectContaining({
          orderId: 'order-123',
          orderNumber: 'ORD-001',
          paymentAmount: 20, // 2000 cents = $20
          paymentMethod: 'stripe',
          transactionId: 'pi_test123'
        }),
        expect.objectContaining({
          userId: 'test-user-123'
        })
      );
    });

    it('should handle payment_intent.payment_failed event', async () => {
      const mockEvent = {
        type: 'payment_intent.payment_failed',
        data: {
          object: {
            id: 'pi_test123',
            amount: 2000,
            metadata: {
              userId: 'test-user-123',
              orderId: 'order-123'
            },
            last_payment_error: {
              message: 'Your card was declined.'
            }
          }
        }
      };

      const mockOrder = {
        id: 'order-123',
        orderNumber: 'ORD-001',
        payment: {
          status: 'pending'
        },
        save: jest.fn().mockResolvedValue()
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      Order.findOne.mockResolvedValue(mockOrder);
      eventBus.publish.mockResolvedValue();

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });

      // Verify order was updated
      expect(mockOrder.payment.status).toBe('failed');
      expect(mockOrder.payment.failureReason).toBe('Your card was declined.');
      expect(mockOrder.save).toHaveBeenCalled();

      // Verify event was published
      expect(eventBus.publish).toHaveBeenCalledWith(
        'order.payment.failed',
        expect.objectContaining({
          orderId: 'order-123',
          orderNumber: 'ORD-001',
          paymentAmount: 20,
          paymentMethod: 'stripe',
          failureReason: 'Your card was declined.'
        }),
        expect.objectContaining({
          userId: 'test-user-123'
        })
      );
    });

    it('should handle payment_intent.canceled event', async () => {
      const mockEvent = {
        type: 'payment_intent.canceled',
        data: {
          object: {
            id: 'pi_test123',
            metadata: {
              userId: 'test-user-123',
              orderId: 'order-123'
            }
          }
        }
      };

      const mockOrder = {
        id: 'order-123',
        payment: {
          status: 'pending'
        },
        save: jest.fn().mockResolvedValue()
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      Order.findOne.mockResolvedValue(mockOrder);

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });

      // Verify order was updated
      expect(mockOrder.payment.status).toBe('cancelled');
      expect(mockOrder.save).toHaveBeenCalled();
    });

    it('should handle unhandled event types gracefully', async () => {
      const mockEvent = {
        type: 'customer.created',
        data: {
          object: {
            id: 'cus_test123'
          }
        }
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });
    });

    it('should handle webhook signature verification failure', async () => {
      stripe.webhooks.constructEvent.mockImplementation(() => {
        throw new Error('Invalid signature');
      });

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'invalid_signature')
        .send(Buffer.from('test_payload'))
        .expect(400);

      expect(response.text).toContain('Webhook Error: Invalid signature');
    });

    it('should handle webhook processing errors', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test123',
            amount: 2000,
            metadata: {
              userId: 'test-user-123',
              orderId: 'order-123'
            }
          }
        }
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      Order.findOne.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(500);

      expect(response.body).toEqual({ error: 'Webhook handler failed' });
    });

    it('should handle payment success without order', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test123',
            amount: 2000,
            metadata: {
              userId: 'test-user-123',
              orderId: 'order-123'
            }
          }
        }
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
      Order.findOne.mockResolvedValue(null); // Order not found

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });

      // Should not publish event if order not found
      expect(eventBus.publish).not.toHaveBeenCalled();
    });

    it('should handle payment success without orderId in metadata', async () => {
      const mockEvent = {
        type: 'payment_intent.succeeded',
        data: {
          object: {
            id: 'pi_test123',
            amount: 2000,
            metadata: {
              userId: 'test-user-123'
              // No orderId
            }
          }
        }
      };

      stripe.webhooks.constructEvent.mockReturnValue(mockEvent);

      const response = await request(app)
        .post('/webhook')
        .set('stripe-signature', 'test_signature')
        .send(Buffer.from('test_payload'))
        .expect(200);

      expect(response.body).toEqual({ received: true });

      // Should not try to find order if no orderId
      expect(Order.findOne).not.toHaveBeenCalled();
      expect(eventBus.publish).not.toHaveBeenCalled();
    });
  });
});
